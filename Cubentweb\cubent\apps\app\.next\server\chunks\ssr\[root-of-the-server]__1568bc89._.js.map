{"version": 3, "sources": [], "sections": [{"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'next-forge';\nconst author: Metadata['authors'] = {\n  name: 'Vercel',\n  url: 'https://vercel.com/',\n};\nconst publisher = 'Vercel';\nconst twitterHandle = '@vercel';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/components/sign-in.tsx"], "sourcesContent": ["import { SignIn as ClerkSignIn } from '@clerk/nextjs';\n\ntype SignInProps = {\n  fallbackRedirectUrl?: string;\n  forceRedirectUrl?: string;\n  signUpFallbackRedirectUrl?: string;\n  signUpForceRedirectUrl?: string;\n  // Legacy props for backward compatibility\n  afterSignInUrl?: string;\n  afterSignUpUrl?: string;\n};\n\nexport const SignIn = ({\n  fallbackRedirectUrl,\n  forceRedirectUrl,\n  signUpFallbackRedirectUrl,\n  signUpForceRedirectUrl,\n  afterSignInUrl,\n  afterSignUpUrl\n}: SignInProps) => {\n  console.log('SignIn component rendering with props:', {\n    fallbackRedirectUrl,\n    forceRedirectUrl,\n    signUpFallbackRedirectUrl,\n    signUpForceRedirectUrl,\n    afterSignInUrl,\n    afterSignUpUrl\n  });\n\n  return (\n    <ClerkSignIn\n      fallbackRedirectUrl={fallbackRedirectUrl || afterSignInUrl}\n      forceRedirectUrl={forceRedirectUrl}\n      signUpFallbackRedirectUrl={signUpFallbackRedirectUrl || afterSignUpUrl}\n      signUpForceRedirectUrl={signUpForceRedirectUrl}\n      appearance={{\n        elements: {\n          header: 'hidden',\n        },\n      }}\n    />\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;;AAYO,MAAM,SAAS,CAAC,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,yBAAyB,EACzB,sBAAsB,EACtB,cAAc,EACd,cAAc,EACF;IACZ,QAAQ,GAAG,CAAC,0CAA0C;QACpD;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6VAAC,gSAAA,CAAA,SAAW;QACV,qBAAqB,uBAAuB;QAC5C,kBAAkB;QAClB,2BAA2B,6BAA6B;QACxD,wBAAwB;QACxB,YAAY;YACV,UAAU;gBACR,QAAQ;YACV;QACF;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28unauthenticated%29/sign-in/%5B%5B...sign-in%5D%5D/page.tsx"], "sourcesContent": ["import { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport { redirect } from 'next/navigation';\nimport { SignIn } from '@repo/auth/components/sign-in';\n\nconst title = 'Welcome back';\nconst description = 'Enter your details to sign in.';\n\nexport const metadata: Metadata = createMetadata({ title, description });\n\ntype SignInPageProps = {\n  searchParams: Promise<{\n    device_id?: string;\n    state?: string;\n    redirect_url?: string;\n  }>;\n};\n\nconst SignInPage = async ({ searchParams }: SignInPageProps) => {\n  const params = await searchParams;\n\n  // Handle device OAuth flow - redirect to login page with device parameters\n  if (params.device_id && params.state) {\n    redirect(`/login?device_id=${params.device_id}&state=${params.state}`);\n  }\n\n  // Handle redirect_url parameter by updating the fallback redirect URL\n  let fallbackRedirectUrl = '/auth-success';\n  if (params.redirect_url) {\n    fallbackRedirectUrl = `/auth-success?redirect_url=${encodeURIComponent(params.redirect_url)}`;\n  }\n\n  return (\n    <>\n      <div className=\"flex flex-col space-y-3 text-center\">\n        <div className=\"space-y-2\">\n          <h1 className=\"font-semibold text-2xl tracking-tight bg-gradient-to-r from-foreground via-orange-400 to-foreground bg-clip-text text-transparent\">\n            {title}\n          </h1>\n          <p className=\"text-muted-foreground text-sm leading-relaxed\">{description}</p>\n        </div>\n        {/* Orange accent line */}\n        <div className=\"mx-auto w-16 h-px bg-gradient-to-r from-transparent via-orange-500 to-transparent opacity-60\" />\n      </div>\n      <SignIn fallbackRedirectUrl={fallbackRedirectUrl} />\n    </>\n  );\n};\n\nexport default SignInPage;\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAAA;AACA;;;;;AAEA,MAAM,QAAQ;AACd,MAAM,cAAc;AAEb,MAAM,WAAqB,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;IAAE;IAAO;AAAY;AAUtE,MAAM,aAAa,OAAO,EAAE,YAAY,EAAmB;IACzD,MAAM,SAAS,MAAM;IAErB,2EAA2E;IAC3E,IAAI,OAAO,SAAS,IAAI,OAAO,KAAK,EAAE;QACpC,CAAA,GAAA,oSAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;IACvE;IAEA,sEAAsE;IACtE,IAAI,sBAAsB;IAC1B,IAAI,OAAO,YAAY,EAAE;QACvB,sBAAsB,CAAC,2BAA2B,EAAE,mBAAmB,OAAO,YAAY,GAAG;IAC/F;IAEA,qBACE;;0BACE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,6VAAC;gCAAE,WAAU;0CAAiD;;;;;;;;;;;;kCAGhE,6VAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,6VAAC,6IAAA,CAAA,SAAM;gBAAC,qBAAqB;;;;;;;;AAGnC;uCAEe", "debugId": null}}]}