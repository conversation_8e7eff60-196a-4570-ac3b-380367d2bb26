{"generatedAt": "2025-06-30T12:23:42.194Z", "sdkVersion": "8.2.7", "inputHash": "f952f1aafa16b7f7a380c7a8541a239b", "schemaHash": "861511b50df8d472344217c1986c324e", "resolvedRef": {"repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "qNNz4p8JMipdRXk4579YJ", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"}}