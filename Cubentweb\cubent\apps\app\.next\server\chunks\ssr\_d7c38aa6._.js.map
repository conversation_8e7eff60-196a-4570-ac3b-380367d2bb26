{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/mode-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ModeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModeToggle() from the server but ModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/mode-toggle.tsx <module evaluation>\",\n    \"ModeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mFACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/mode-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ModeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModeToggle() from the server but ModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/mode-toggle.tsx\",\n    \"ModeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28unauthenticated%29/layout.tsx"], "sourcesContent": ["import { ModeToggle } from '@repo/design-system/components/mode-toggle';\nimport { CommandIcon } from 'lucide-react';\nimport type { ReactNode } from 'react';\n\ntype AuthLayoutProps = {\n  readonly children: ReactNode;\n};\n\nconst AuthLayout = ({ children }: AuthLayoutProps) => (\n  <div className=\"relative min-h-dvh w-full overflow-hidden bg-gradient-to-br from-black via-gray-900 to-gray-950\">\n    {/* Orange ambient glow effects */}\n    <div className=\"absolute inset-0 pointer-events-none\">\n      <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl\" />\n      <div className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-orange-600/8 rounded-full blur-3xl\" />\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-orange-400/3 rounded-full blur-2xl\" />\n    </div>\n\n    {/* Background grid pattern */}\n    <div className=\"absolute inset-0 pointer-events-none opacity-30\">\n      {/* Programming Language Icons scattered in background */}\n      <div className=\"absolute w-8 h-8 text-orange-400\" style={{ left: '5%', top: '15%' }}>\n        <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"m2 2h12v12h-12v-12m3.1533 10.027c0.26667 0.56667 0.79333 1.0333 1.6933 1.0333 1 0 1.6867-0.53333 1.6867-1.7v-3.8533h-1.1333v3.8267c0 0.57333-0.23333 0.72-0.6 0.72-0.38667 0-0.54667-0.26667-0.72667-0.58l-0.92 0.55333m3.9867-0.12c0.33333 0.65333 1.0067 1.1533 2.06 1.1533 1.0667 0 1.8667-0.55333 1.8667-1.5733 0-0.94-0.54-1.36-1.5-1.7733l-0.28-0.12c-0.48667-0.20667-0.69333-0.34667-0.69333-0.68 0-0.27333 0.20667-0.48667 0.54-0.48667 0.32 0 0.53333 0.14 0.72667 0.48667l0.87333-0.58c-0.36667-0.64-0.88667-0.88667-1.6-0.88667-1.0067 0-1.6533 0.64-1.6533 1.4867 0 0.92 0.54 1.3533 1.3533 1.7l0.28 0.12c0.52 0.22667 0.82667 0.36667 0.82667 0.75333 0 0.32-0.3 0.55333-0.76667 0.55333-0.55333 0-0.87333-0.28667-1.1133-0.68667z\" fill=\"currentColor\" strokeWidth=\".66667\"/>\n        </svg>\n      </div>\n      <div className=\"absolute w-6 h-6 text-orange-300\" style={{ left: '85%', top: '12%' }}>\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\n          <path d=\"M9.86 2A2.86 2.86 0 0 0 7 4.86v1.68h4.29c.39 0 .71.57.71.96H4.86A2.86 2.86 0 0 0 2 10.36v3.781a2.86 2.86 0 0 0 2.86 2.86h1.18v-2.68a2.85 2.85 0 0 1 2.85-2.86h5.25c1.58 0 2.86-1.271 2.86-2.851V4.86A2.86 2.86 0 0 0 14.14 2zm-.72 1.61c.4 0 .72.12.72.71s-.32.891-.72.891c-.39 0-.71-.3-.71-.89s.32-.711.71-.711z\" fill=\"currentColor\"/>\n          <path d=\"M17.959 7v2.68a2.85 2.85 0 0 1-2.85 2.859H9.86A2.85 2.85 0 0 0 7 15.389v3.75a2.86 2.86 0 0 0 2.86 2.86h4.28A2.86 2.86 0 0 0 17 19.14v-1.68h-4.291c-.39 0-.709-.57-.709-.96h7.14A2.86 2.86 0 0 0 22 13.64V9.86A2.86 2.86 0 0 0 19.14 7zM8.32 11.513l-.004.004c.012-.002.025-.001.038-.004zm6.54 7.276c.39 0 .71.3.71.89a.71.71 0 0 1-.71.71c-.4 0-.72-.12-.72-.71s.32-.89.72-.89z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '15%', top: '75%' }}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\n          <path d=\"M16,12c7.44405,0,12,2.58981,12,4s-4.55595,4-12,4S4,17.41019,4,16,8.556,12,16,12m0-2C8.268,10,2,12.68629,2,16s6.268,6,14,6,14-2.68629,14-6-6.268-6-14-6Z\" fill=\"currentColor\"/>\n          <path d=\"M16,14a2,2,0,1,0,2,2,2,2,0,0,0-2-2Z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      <div className=\"absolute w-6 h-6 text-orange-500\" style={{ left: '75%', top: '80%' }}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\n          <path d=\"M16,30A14,14,0,1,1,30,16,14,14,0,0,1,16,30ZM16,4A12,12,0,1,0,28,16,12,12,0,0,0,16,4Z\" fill=\"currentColor\"/>\n          <path d=\"M16,24.414l-6.707-6.707,1.414-1.414L16,21.586l5.293-5.293,1.414,1.414Z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n    </div>\n\n    {/* Dashed grid lines with orange accents */}\n    <div className=\"absolute inset-0 pointer-events-none z-10\">\n      {/* Vertical dashed lines */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          left: '20%',\n          background: 'repeating-linear-gradient(to bottom, rgba(249, 115, 22, 0.4) 0 5px, transparent 5px 11px)'\n        }}\n      />\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          right: '20%',\n          background: 'repeating-linear-gradient(to bottom, rgba(249, 115, 22, 0.4) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Horizontal dashed lines */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '20%',\n          left: '20%',\n          right: '20%',\n          background: 'repeating-linear-gradient(to right, rgba(249, 115, 22, 0.4) 0 5px, transparent 5px 11px)'\n        }}\n      />\n      <div\n        className=\"absolute h-px\"\n        style={{\n          bottom: '20%',\n          left: '20%',\n          right: '20%',\n          background: 'repeating-linear-gradient(to right, rgba(249, 115, 22, 0.4) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Additional orange accent lines */}\n      <div\n        className=\"absolute h-px\"\n        style={{\n          top: '50%',\n          left: '10%',\n          right: '10%',\n          background: 'repeating-linear-gradient(to right, rgba(249, 115, 22, 0.2) 0 3px, transparent 3px 8px)'\n        }}\n      />\n    </div>\n\n    {/* Mode toggle in top right */}\n    <div className=\"absolute top-4 right-4 z-30\">\n      <ModeToggle />\n    </div>\n\n    {/* Centered authentication modal */}\n    <div className=\"relative z-20 flex min-h-dvh items-center justify-center p-4\">\n      <div className=\"w-full max-w-md\">\n        {/* Modal container with white background and reduced opacity */}\n        <div className=\"relative bg-white/70 rounded-2xl shadow-2xl shadow-black/50 p-8\">\n          {/* Content */}\n          <div className=\"relative z-10 space-y-6\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nexport default AuthLayout;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB,iBAC/C,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAChF,cAAA,6VAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCAC3F,cAAA,6VAAC;gCAAK,GAAE;gCAAktB,MAAK;gCAAe,aAAY;;;;;;;;;;;;;;;;kCAG9vB,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAqT,MAAK;;;;;;8CAClU,6VAAC;oCAAK,GAAE;oCAAmX,MAAK;;;;;;;;;;;;;;;;;kCAGpY,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAA0J,MAAK;;;;;;8CACvK,6VAAC;oCAAK,GAAE;oCAAsC,MAAK;;;;;;;;;;;;;;;;;kCAGvD,6VAAC;wBAAI,WAAU;wBAAmC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCACjF,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAuF,MAAK;;;;;;8CACpG,6VAAC;oCAAK,GAAE;oCAAyE,MAAK;;;;;;;;;;;;;;;;;;;;;;;0BAM5F,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM;4BACN,YAAY;wBACd;;;;;;kCAEF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAEF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;;;;;;;0BAKJ,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC,6JAAA,CAAA,aAAU;;;;;;;;;;0BAIb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BAEb,cAAA,6VAAC;wBAAI,WAAU;kCAEb,cAAA,6VAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAQE", "debugId": null}}]}