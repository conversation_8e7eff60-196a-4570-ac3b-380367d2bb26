module.exports = {

"[project]/packages/internationalization/dictionaries/en.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"web\":{\"global\":{\"primaryCta\":\"Get Started\",\"secondaryCta\":\"Download Extension\"},\"header\":{\"home\":\"Home\",\"product\":{\"title\":\"Features\",\"description\":\"Discover the powerful AI coding capabilities that make development faster and smarter.\",\"pricing\":\"Pricing\"},\"blog\":\"Blog\",\"docs\":\"Documentation\",\"contact\":\"Support\",\"signIn\":\"Sign in\",\"signUp\":\"Download Now\"},\"home\":{\"meta\":{\"title\":\"AI-Powered Coding Agent for VS Code\",\"description\":\"Meet Cubent Coder - the autonomous AI coding assistant that lives in your editor. Generate code, debug issues, write documentation, and automate tasks with natural language commands.\"},\"hero\":{\"announcement\":\"Cubent.Dev 0.21 Released\"},\"cases\":{\"title\":\"Trusted by Developers Worldwide\"},\"features\":{\"title\":\"Powerful AI Coding Capabilities\",\"description\":\"Discover how Cubent Coder's advanced AI features can revolutionize your development workflow.\",\"items\":[{\"title\":\"Natural Language Coding\",\"description\":\"Generate, refactor, and debug code using simple natural language commands. Just describe what you want and watch Cubent Coder bring it to life.\"},{\"title\":\"Autonomous Agent Mode\",\"description\":\"Let Cubent Coder work independently on complex tasks, making decisions and executing actions with minimal supervision.\"},{\"title\":\"Smart File Operations\",\"description\":\"Read, write, and modify files across your entire workspace. Cubent Coder understands your project structure and maintains context.\"},{\"title\":\"Terminal Integration\",\"description\":\"Execute commands, run tests, and manage your development environment directly through AI-powered terminal interactions.\"}]},\"stats\":{\"title\":\"Proven Developer Impact\",\"description\":\"Join thousands of developers who have transformed their coding workflow with Cubent Coder. Our AI assistant has helped developers reduce development time by 60% and increase code quality by 45% on average.\",\"items\":[{\"title\":\"Active developers\",\"metric\":\"50000\",\"delta\":\"25\",\"type\":\"unit\"},{\"title\":\"Code generations daily\",\"metric\":\"500000\",\"delta\":\"15\",\"type\":\"unit\"},{\"title\":\"Time saved per week\",\"metric\":\"12\",\"delta\":\"30\",\"type\":\"unit\"},{\"title\":\"Bug reduction rate\",\"metric\":\"85\",\"delta\":\"20\",\"type\":\"unit\"}]},\"testimonials\":{\"title\":\"Loved by Developers Worldwide\",\"items\":[{\"title\":\"Revolutionary coding experience\",\"description\":\"Cubent Coder has completely transformed how I approach development. The AI understands context better than any tool I've used before.\",\"author\":{\"name\":\"Sarah Chen\",\"image\":\"https://github.com/haydenbleasel.png\"}},{\"title\":\"Incredible productivity boost\",\"description\":\"I'm shipping features 3x faster with Cubent Coder. The autonomous mode is like having a senior developer pair programming with me 24/7.\",\"author\":{\"name\":\"Marcus Rodriguez\",\"image\":\"https://github.com/leerob.png\"}},{\"title\":\"Game-changing for teams\",\"description\":\"Our entire team adopted Cubent Coder and our code quality improved dramatically. The documentation generation alone saves us hours.\",\"author\":{\"name\":\"Alex Thompson\",\"image\":\"https://github.com/shadcn.png\"}},{\"title\":\"Best VS Code extension ever\",\"description\":\"I've tried every AI coding tool out there, but Cubent Coder's natural language interface and smart automation are unmatched.\",\"author\":{\"name\":\"Emily Zhang\",\"image\":\"https://github.com/pontusab.png\"}}]},\"faq\":{\"title\":\"Frequently Asked Questions\",\"description\":\"Get quick answers to common questions about Cubent Coder. Learn how to get started, understand the features, and make the most of your AI coding assistant.\",\"cta\":\"Need help? Join our Discord\",\"items\":[{\"question\":\"What is Cubent Coder?\",\"answer\":\"Cubent Coder is an AI-powered autonomous coding agent that works as a VS Code extension. It can generate code, debug issues, write documentation, and automate development tasks using natural language commands.\"},{\"question\":\"How do I install Cubent Coder?\",\"answer\":\"Simply install the extension from the VS Code Marketplace, connect your preferred AI provider (OpenAI, Anthropic, or custom), and start coding with AI assistance immediately.\"},{\"question\":\"What AI models does it support?\",\"answer\":\"Cubent Coder works with any OpenAI-compatible API, including GPT-4, Claude, and custom local models. You can easily switch between providers based on your needs.\"},{\"question\":\"Is my code secure?\",\"answer\":\"Yes, your code security is our priority. Cubent Coder processes code locally in your VS Code environment and only sends necessary context to your chosen AI provider. You maintain full control over what data is shared.\"},{\"question\":\"Can I use custom modes?\",\"answer\":\"Absolutely! Cubent Coder supports unlimited custom modes, allowing you to create specialized AI personas for security auditing, code review, documentation, or any specific development task.\"}]},\"cta\":{\"title\":\"Start Coding with AI Today\",\"description\":\"Join thousands of developers who have already transformed their coding workflow with Cubent Coder. Download the extension, connect your AI provider, and experience the future of development in minutes.\"}},\"blog\":{\"meta\":{\"title\":\"Cubent Coder Blog\",\"description\":\"Latest updates, tutorials, and insights about AI-powered development with Cubent Coder.\"}},\"contact\":{\"meta\":{\"title\":\"Get Support & Connect\",\"description\":\"Need help with Cubent Coder? Join our community, report issues, or get in touch with our team for support and guidance.\"},\"hero\":{\"benefits\":[{\"title\":\"Community Support\",\"description\":\"Join our active Discord and Reddit communities for real-time help, tips, and discussions with fellow developers.\"},{\"title\":\"Comprehensive Documentation\",\"description\":\"Access detailed guides, tutorials, and API references to help you master Cubent Coder's features.\"},{\"title\":\"Open Source Contribution\",\"description\":\"Contribute to the project on GitHub, report issues, or request new features to help improve Cubent Coder for everyone.\"}],\"form\":{\"title\":\"Get in touch\",\"date\":\"Date\",\"firstName\":\"First name\",\"lastName\":\"Last name\",\"resume\":\"Upload file\",\"cta\":\"Send message\"}}}}}"));}}),

};