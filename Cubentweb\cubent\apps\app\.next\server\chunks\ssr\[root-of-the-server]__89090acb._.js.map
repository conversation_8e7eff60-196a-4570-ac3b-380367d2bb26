{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    client: {\n      NEXT_PUBLIC_POSTHOG_KEY: z.string().startsWith('phc_').optional(),\n      NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().startsWith('G-').optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\n      NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,yBAAyB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,QAAQ;YAC/D,0BAA0B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACnD,+BAA+B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ;QACrE;QACA,YAAY;YACV,uBAAuB;YACvB,wBAAwB;YACxB,6BAA6B;QAC/B;IACF", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/client.tsx"], "sourcesContent": ["'use client';\n\nimport posthog, { type PostHog } from 'posthog-js';\nimport { PostHogProvider as PostHogProviderRaw } from 'posthog-js/react';\nimport type { ReactNode } from 'react';\nimport { useEffect } from 'react';\nimport { keys } from '../keys';\n\ntype PostHogProviderProps = {\n  readonly children: ReactNode;\n};\n\nexport const PostHogProvider = (\n  properties: Omit<PostHogProviderProps, 'client'>\n) => {\n  useEffect(() => {\n    try {\n      const envKeys = keys();\n      if (envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST) {\n        posthog.init(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n          api_host: '/ingest',\n          ui_host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n          person_profiles: 'identified_only',\n          capture_pageview: false, // Disable automatic pageview capture, as we capture manually\n          capture_pageleave: true, // Overrides the `capture_pageview` setting\n        }) as PostHog;\n      } else {\n        console.warn('PostHog environment variables not configured. Analytics disabled.');\n      }\n    } catch (error) {\n      console.warn('PostHog initialization failed:', error);\n    }\n  }, []);\n\n  return <PostHogProviderRaw client={posthog} {...properties} />;\n};\n\nexport { usePostHog as useAnalytics } from 'posthog-js/react';\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAYO,MAAM,kBAAkB,CAC7B;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;YACnB,IAAI,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,EAAE;gBACvE,4PAAA,CAAA,UAAO,CAAC,IAAI,CAAC,QAAQ,uBAAuB,EAAE;oBAC5C,UAAU;oBACV,SAAS,QAAQ,wBAAwB;oBACzC,iBAAiB;oBACjB,kBAAkB;oBAClB,mBAAmB;gBACrB;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;QACjD;IACF,GAAG,EAAE;IAEL,qBAAO,6VAAC,2QAAA,CAAA,kBAAkB;QAAC,QAAQ,4PAAA,CAAA,UAAO;QAAG,GAAG,UAAU;;;;;;AAC5D", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/provider.tsx"], "sourcesContent": ["'use client';\n\nimport { Clerk<PERSON><PERSON><PERSON> } from '@clerk/nextjs';\nimport { dark } from '@clerk/themes';\nimport type { Theme } from '@clerk/types';\nimport { useTheme } from 'next-themes';\nimport type { ComponentProps } from 'react';\n\ntype AuthProviderProperties = ComponentProps<typeof ClerkProvider> & {\n  privacyUrl?: string;\n  termsUrl?: string;\n  helpUrl?: string;\n};\n\nexport const AuthProvider = ({\n  privacyUrl,\n  termsUrl,\n  helpUrl,\n  ...properties\n}: AuthProviderProperties) => {\n  const { resolvedTheme } = useTheme();\n  const isDark = resolvedTheme === 'dark';\n  const baseTheme = isDark ? dark : undefined;\n\n  const variables: Theme['variables'] = {\n    fontFamily: 'var(--font-sans)',\n    fontFamilyButtons: 'var(--font-sans)',\n    fontWeight: {\n      bold: 'var(--font-weight-bold)',\n      normal: 'var(--font-weight-normal)',\n      medium: 'var(--font-weight-medium)',\n    },\n    // Orange color theming\n    colorPrimary: '#f97316', // orange-500\n    colorSuccess: '#10b981', // emerald-500\n    colorWarning: '#f59e0b', // amber-500\n    colorDanger: '#ef4444', // red-500\n    colorNeutral: '#6b7280', // gray-500\n    colorTextOnPrimaryBackground: '#ffffff',\n    colorTextSecondary: '#9ca3af', // gray-400\n    colorInputBackground: 'rgba(0, 0, 0, 0.1)',\n    colorInputText: 'var(--foreground)',\n    borderRadius: '0.75rem', // rounded-xl\n    spacingUnit: '1rem',\n  };\n\n  const elements: Theme['elements'] = {\n    dividerLine: 'bg-border',\n    socialButtonsIconButton: 'bg-card hover:bg-orange-500/10 border-orange-500/20 transition-colors',\n    navbarButton: 'text-foreground',\n    organizationSwitcherTrigger__open: 'bg-background',\n    organizationPreviewMainIdentifier: 'text-foreground',\n    organizationSwitcherTriggerIcon: 'text-muted-foreground',\n    organizationPreview__organizationSwitcherTrigger: 'gap-2',\n    organizationPreviewAvatarContainer: 'shrink-0',\n    // Form elements with orange accents\n    formButtonPrimary: 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg transition-all duration-200',\n    formFieldInput: 'border-gray-600 focus:border-orange-500 focus:ring-orange-500/20 bg-background/50 backdrop-blur-sm',\n    card: 'bg-transparent shadow-none border-none',\n    rootBox: 'bg-transparent',\n    // Header styling\n    headerTitle: 'text-foreground font-semibold text-xl',\n    headerSubtitle: 'text-muted-foreground text-sm',\n    // Footer links with orange hover\n    footerActionLink: 'text-orange-400 hover:text-orange-300 transition-colors',\n  };\n\n  const layout: Theme['layout'] = {\n    privacyPageUrl: privacyUrl,\n    termsPageUrl: termsUrl,\n    helpPageUrl: helpUrl,\n  };\n\n  return (\n    <ClerkProvider\n      {...properties}\n      appearance={{ layout, baseTheme, elements, variables }}\n    />\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAcO,MAAM,eAAe,CAAC,EAC3B,UAAU,EACV,QAAQ,EACR,OAAO,EACP,GAAG,YACoB;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,SAAS,kBAAkB;IACjC,MAAM,YAAY,SAAS,kOAAA,CAAA,OAAI,GAAG;IAElC,MAAM,YAAgC;QACpC,YAAY;QACZ,mBAAmB;QACnB,YAAY;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,uBAAuB;QACvB,cAAc;QACd,cAAc;QACd,cAAc;QACd,aAAa;QACb,cAAc;QACd,8BAA8B;QAC9B,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,MAAM,WAA8B;QAClC,aAAa;QACb,yBAAyB;QACzB,cAAc;QACd,mCAAmC;QACnC,mCAAmC;QACnC,iCAAiC;QACjC,kDAAkD;QAClD,oCAAoC;QACpC,oCAAoC;QACpC,mBAAmB;QACnB,gBAAgB;QAChB,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,aAAa;QACb,gBAAgB;QAChB,iCAAiC;QACjC,kBAAkB;IACpB;IAEA,MAAM,SAA0B;QAC9B,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,qBACE,6VAAC,mRAAA,CAAA,gBAAa;QACX,GAAG,UAAU;QACd,YAAY;YAAE;YAAQ;YAAW;YAAU;QAAU;;;;;;AAG3D", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6VAAC,wQAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/log.ts"], "sourcesContent": ["import { log as logtail } from '@logtail/next';\n\nexport const log = process.env.NODE_ENV === 'production' ? logtail : console;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,MAAM,6EAAkD", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/error.ts"], "sourcesContent": ["import { captureException } from '@sentry/nextjs';\nimport { log } from './log';\n\nexport const parseError = (error: unknown): string => {\n  let message = 'An error occurred';\n\n  if (error instanceof Error) {\n    message = error.message;\n  } else if (error && typeof error === 'object' && 'message' in error) {\n    message = error.message as string;\n  } else {\n    message = String(error);\n  }\n\n  try {\n    captureException(error);\n    log.error(`Parsing error: ${message}`);\n  } catch (newError) {\n    // biome-ignore lint/suspicious/noConsole: Need console here\n    console.error('Error parsing error:', newError);\n  }\n\n  return message;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI,UAAU;IAEd,IAAI,iBAAiB,OAAO;QAC1B,UAAU,MAAM,OAAO;IACzB,OAAO,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;QACnE,UAAU,MAAM,OAAO;IACzB,OAAO;QACL,UAAU,OAAO;IACnB;IAEA,IAAI;QACF,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE;QACjB,gIAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;IACvC,EAAE,OAAO,UAAU;QACjB,4DAA4D;QAC5D,QAAQ,KAAK,CAAC,wBAAwB;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/lib/utils.ts"], "sourcesContent": ["import { parseError } from '@repo/observability/error';\nimport { clsx } from 'clsx';\nimport type { ClassValue } from 'clsx';\nimport { toast } from 'sonner';\nimport { twMerge } from 'tailwind-merge';\n\nexport const cn = (...inputs: ClassValue[]): string => twMerge(clsx(inputs));\n\nexport const capitalize = (str: string) =>\n  str.charAt(0).toUpperCase() + str.slice(1);\n\nexport const handleError = (error: unknown): void => {\n  const message = parseError(error);\n\n  toast.error(message);\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;AAEO,MAAM,KAAK,CAAC,GAAG,SAAiC,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AAE7D,MAAM,aAAa,CAAC,MACzB,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AAEnC,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;AACd", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6VAAC,6QAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6VAAC;kBACC,cAAA,6VAAC,6QAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6VAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6VAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,6VAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6VAAC,6QAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/CrossDomainAuthSync.tsx"], "sourcesContent": ["'use client';\n\nimport { useUser } from '@clerk/nextjs';\nimport { useEffect } from 'react';\n\nexport function CrossDomainAuthSync() {\n  const { isLoaded, isSignedIn, user } = useUser();\n\n  useEffect(() => {\n    async function manageCrossDomainToken() {\n      if (isLoaded) {\n        if (isSignedIn && user) {\n          // User is logged in - set the token\n          try {\n            console.log('[CROSS-DOMAIN] Setting auth token for cubent.dev...');\n\n            const response = await fetch('/api/auth/set-cross-domain-token', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n            });\n\n            if (response.ok) {\n              console.log('[CROSS-DOMAIN] Auth token set successfully');\n            } else {\n              console.error('[CROSS-DOMAIN] Failed to set auth token:', response.status);\n            }\n          } catch (error) {\n            console.error('[CROSS-DOMAIN] Error setting auth token:', error);\n          }\n        } else {\n          // User is logged out - clear the token\n          try {\n            console.log('[CROSS-DOMAIN] Clearing auth token for cubent.dev...');\n\n            const response = await fetch('/api/auth/clear-cross-domain-token', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n            });\n\n            if (response.ok) {\n              console.log('[CROSS-DOMAIN] Auth token cleared successfully');\n            } else {\n              console.error('[CROSS-DOMAIN] Failed to clear auth token:', response.status);\n            }\n          } catch (error) {\n            console.error('[CROSS-DOMAIN] Error clearing auth token:', error);\n          }\n        }\n      }\n    }\n\n    manageCrossDomainToken();\n  }, [isLoaded, isSignedIn, user]);\n\n  // This component doesn't render anything\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;IAE7C,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI,UAAU;gBACZ,IAAI,cAAc,MAAM;oBACtB,oCAAoC;oBACpC,IAAI;wBACF,QAAQ,GAAG,CAAC;wBAEZ,MAAM,WAAW,MAAM,MAAM,oCAAoC;4BAC/D,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,QAAQ,GAAG,CAAC;wBACd,OAAO;4BACL,QAAQ,KAAK,CAAC,4CAA4C,SAAS,MAAM;wBAC3E;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4CAA4C;oBAC5D;gBACF,OAAO;oBACL,uCAAuC;oBACvC,IAAI;wBACF,QAAQ,GAAG,CAAC;wBAEZ,MAAM,WAAW,MAAM,MAAM,sCAAsC;4BACjE,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,QAAQ,GAAG,CAAC;wBACd,OAAO;4BACL,QAAQ,KAAK,CAAC,8CAA8C,SAAS,MAAM;wBAC7E;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6CAA6C;oBAC7D;gBACF;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAU;QAAY;KAAK;IAE/B,yCAAyC;IACzC,OAAO;AACT", "debugId": null}}]}